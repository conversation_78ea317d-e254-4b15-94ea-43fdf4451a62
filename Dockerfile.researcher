# Dockerfile for Researcher Agent
FROM python:3.13-slim

WORKDIR /app

# System dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    curl ca-certificates git && \
    rm -rf /var/lib/apt/lists/*

# Copy and install Python dependencies
COPY requirements.txt ./
RUN pip install --no-cache-dir -r requirements.txt

# Install a2a-sdk from PyPI

# Copy agent code and configuration
COPY agents/researcher /app/agents/researcher
COPY well_known /app/well_known

# Create data directories
RUN mkdir -p /data/conversations

# Set Python path
ENV PYTHONPATH=/app:$PYTHONPATH

EXPOSE 8102

CMD ["python", "-m", "agents.researcher.server"]
